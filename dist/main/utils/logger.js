"use strict";
/**
 * Safe logging utility that handles cases where stdout/stderr streams are unavailable
 * This prevents EIO errors when the process is being terminated or console is disconnected
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.safeDebug = exports.safeError = exports.safeWarn = exports.safeInfo = exports.safeLog = exports.SafeLogger = void 0;
class SafeLogger {
    /**
     * Safely log an info message
     */
    static info(...args) {
        try {
            console.log(...args);
        }
        catch (error) {
            // Silently ignore console write errors (e.g., when stdout is unavailable)
        }
    }
    /**
     * Safely log a warning message
     */
    static warn(...args) {
        try {
            console.warn(...args);
        }
        catch (error) {
            // Silently ignore console write errors (e.g., when stdout is unavailable)
        }
    }
    /**
     * Safely log an error message
     */
    static error(...args) {
        try {
            console.error(...args);
        }
        catch (error) {
            // Silently ignore console write errors (e.g., when stdout is unavailable)
        }
    }
    /**
     * Safely log a debug message
     */
    static debug(...args) {
        try {
            console.debug(...args);
        }
        catch (error) {
            // Silently ignore console write errors (e.g., when stdout is unavailable)
        }
    }
    /**
     * Safely log a generic message
     */
    static log(...args) {
        try {
            console.log(...args);
        }
        catch (error) {
            // Silently ignore console write errors (e.g., when stdout is unavailable)
        }
    }
    /**
     * Check if console output is available
     * This can be useful for determining if the process is being terminated
     */
    static isConsoleAvailable() {
        try {
            // Try a minimal write operation to test if console is available
            console.log('');
            return true;
        }
        catch (error) {
            return false;
        }
    }
    /**
     * Safely attempt to flush any pending console output
     * Useful before process termination
     */
    static flush() {
        try {
            if (process.stdout && typeof process.stdout.write === 'function') {
                process.stdout.write('');
            }
            if (process.stderr && typeof process.stderr.write === 'function') {
                process.stderr.write('');
            }
        }
        catch (error) {
            // Silently ignore flush errors
        }
    }
}
exports.SafeLogger = SafeLogger;
// Export convenience functions for easier migration
exports.safeLog = SafeLogger.log;
exports.safeInfo = SafeLogger.info;
exports.safeWarn = SafeLogger.warn;
exports.safeError = SafeLogger.error;
exports.safeDebug = SafeLogger.debug;
