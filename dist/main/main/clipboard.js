"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClipboardManager = void 0;
const electron_1 = require("electron");
const uuid_1 = require("uuid");
const logger_1 = require("../utils/logger");
class ClipboardManager {
    constructor() {
        this.history = [];
        this.lastClipboard = '';
        this.watchInterval = null;
        this.maxHistorySize = 100;
        this.watchIntervalMs = 1000;
        this.loadHistory();
    }
    start() {
        if (this.watchInterval) {
            return; // Already watching
        }
        logger_1.SafeLogger.info('Starting clipboard monitoring...');
        // Get initial clipboard content
        this.lastClipboard = electron_1.clipboard.readText();
        // Start watching for clipboard changes
        this.watchInterval = setInterval(() => {
            this.checkClipboard();
        }, this.watchIntervalMs);
    }
    stop() {
        if (this.watchInterval) {
            clearInterval(this.watchInterval);
            this.watchInterval = null;
            logger_1.SafeLogger.info('Stopped clipboard monitoring');
        }
    }
    checkClipboard() {
        try {
            const currentText = electron_1.clipboard.readText();
            // Check if clipboard content has changed
            if (currentText !== this.lastClipboard && currentText.trim()) {
                this.addToHistory(currentText, 'text');
                this.lastClipboard = currentText;
            }
            // TODO: Add support for images and files in future versions
            // const currentImage = clipboard.readImage();
            // if (!currentImage.isEmpty()) {
            //   // Handle image clipboard
            // }
        }
        catch (error) {
            logger_1.SafeLogger.error('Error checking clipboard:', error);
        }
    }
    addToHistory(content, type) {
        // Don't add if it's already the most recent item
        if (this.history.length > 0 && this.history[0].content === content) {
            return;
        }
        // Don't add empty or very short content
        if (content.trim().length < 2) {
            return;
        }
        // Don't add passwords or sensitive data (basic heuristics)
        if (this.isSensitiveContent(content)) {
            return;
        }
        const clipboardItem = {
            id: (0, uuid_1.v4)(),
            content: content.trim(),
            type,
            created_at: new Date().toISOString(),
        };
        // Add to beginning of history
        this.history.unshift(clipboardItem);
        // Limit history size
        if (this.history.length > this.maxHistorySize) {
            this.history = this.history.slice(0, this.maxHistorySize);
        }
        console.log(`Added to clipboard history: ${content.substring(0, 50)}...`);
        this.saveHistory();
    }
    isSensitiveContent(content) {
        const sensitivePatterns = [
            /password/i,
            /secret/i,
            /token/i,
            /api[_-]?key/i,
            /private[_-]?key/i,
            /ssh[_-]?key/i,
            /credit[_-]?card/i,
            /ssn/i,
            /social[_-]?security/i,
            // Common password patterns
            /^[a-zA-Z0-9!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]{8,}$/,
        ];
        return sensitivePatterns.some(pattern => pattern.test(content));
    }
    getHistory() {
        return [...this.history]; // Return a copy
    }
    clearHistory() {
        this.history = [];
        this.saveHistory();
        console.log('Clipboard history cleared');
    }
    removeItem(id) {
        this.history = this.history.filter(item => item.id !== id);
        this.saveHistory();
    }
    copyToClipboard(content) {
        electron_1.clipboard.writeText(content);
        this.lastClipboard = content;
    }
    saveHistory() {
        try {
            // In a real app, you might want to save to a file or database
            // For now, we'll keep it in memory only
            // TODO: Implement persistent storage
        }
        catch (error) {
            logger_1.SafeLogger.error('Error saving clipboard history:', error);
        }
    }
    loadHistory() {
        try {
            // TODO: Load from persistent storage
            // For now, start with empty history
            this.history = [];
        }
        catch (error) {
            logger_1.SafeLogger.error('Error loading clipboard history:', error);
            this.history = [];
        }
    }
    searchHistory(query) {
        if (!query.trim()) {
            return this.getHistory();
        }
        const searchTerm = query.toLowerCase();
        return this.history.filter(item => item.content.toLowerCase().includes(searchTerm));
    }
}
exports.ClipboardManager = ClipboardManager;
