import Database from 'better-sqlite3';
import { readFileSync } from 'fs';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { Note, Folder, NoteNode, SearchResult } from '../shared/types';

export class NotesDB {
  private db: Database.Database;

  constructor(dbPath: string) {
    this.db = new Database(dbPath);
    this.db.pragma('journal_mode = WAL');
    this.db.pragma('foreign_keys = ON');
    this.initTables();
  }

  private initTables() {
    try {
      // Read and execute schema
      const schemaPath = join(__dirname, 'schema.sql');
      const schema = readFileSync(schemaPath, 'utf-8');
      
      // Check if we need to migrate existing database
      this.migrateDatabase();
      
      this.db.exec(schema);
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Error initializing database:', error);
      throw error;
    }
  }

  private migrateDatabase() {
    try {
      // Check if parent_id column exists
      const tableInfo = this.db.prepare("PRAGMA table_info(notes)").all() as any[];
      const hasParentId = tableInfo.some(col => col.name === 'parent_id');
      const hasIsFolder = tableInfo.some(col => col.name === 'is_folder');
      const hasIsExpanded = tableInfo.some(col => col.name === 'is_expanded');
      const hasIsTemporary = tableInfo.some(col => col.name === 'is_temporary');

      if (!hasParentId || !hasIsFolder || !hasIsExpanded || !hasIsTemporary) {
        console.log('Migrating database to tree structure...');
        
        // Add new columns if they don't exist
        if (!hasParentId) {
          this.db.exec('ALTER TABLE notes ADD COLUMN parent_id TEXT REFERENCES notes(id)');
        }
        if (!hasIsFolder) {
          this.db.exec('ALTER TABLE notes ADD COLUMN is_folder BOOLEAN DEFAULT FALSE');
        }
        if (!hasIsExpanded) {
          this.db.exec('ALTER TABLE notes ADD COLUMN is_expanded BOOLEAN DEFAULT TRUE');
        }
        if (!hasIsTemporary) {
          this.db.exec('ALTER TABLE notes ADD COLUMN is_temporary BOOLEAN DEFAULT FALSE');
        }

        // Create indexes for new columns
        this.db.exec('CREATE INDEX IF NOT EXISTS idx_notes_parent_id ON notes(parent_id)');
        this.db.exec('CREATE INDEX IF NOT EXISTS idx_notes_is_folder ON notes(is_folder)');

        // Temporarily disable foreign keys for migration
        this.db.exec('PRAGMA foreign_keys = OFF');
        
        // Set all existing notes to have 'root' as parent if they don't have one
        this.db.exec("UPDATE notes SET parent_id = 'root' WHERE parent_id IS NULL");
        
        // Re-enable foreign keys
        this.db.exec('PRAGMA foreign_keys = ON');

        console.log('Database migration completed');
      }
    } catch (error) {
      console.error('Error during database migration:', error);
      // Don't throw here, let the main init continue
    }
  }

  // Note operations
  async createNote(content: string, title?: string, parentId?: string, isTemporary?: boolean): Promise<string> {
    const id = uuidv4();
    const now = new Date().toISOString();
    
    const stmt = this.db.prepare(`
      INSERT INTO notes (id, content, title, parent_id, is_folder, is_temporary, created_at, updated_at) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    try {
      stmt.run(id, content, title || null, parentId || 'root', 0, isTemporary ? 1 : 0, now, now);
      return id;
    } catch (error) {
      console.error('Error creating note:', error);
      throw error;
    }
  }

  async updateNote(id: string, content: string, title?: string): Promise<void> {
    const now = new Date().toISOString();
    
    const stmt = this.db.prepare(`
      UPDATE notes 
      SET content = ?, title = ?, updated_at = ?, is_temporary = FALSE
      WHERE id = ? AND is_deleted = FALSE
    `);
    
    try {
      const result = stmt.run(content, title || null, now, id);
      if (result.changes === 0) {
        throw new Error('Note not found or already deleted');
      }
    } catch (error) {
      console.error('Error updating note:', error);
      throw error;
    }
  }

  async deleteNote(id: string): Promise<void> {
    const stmt = this.db.prepare(`
      UPDATE notes 
      SET is_deleted = TRUE, updated_at = ?
      WHERE id = ?
    `);
    
    try {
      const result = stmt.run(new Date().toISOString(), id);
      if (result.changes === 0) {
        throw new Error('Note not found');
      }
    } catch (error) {
      console.error('Error deleting note:', error);
      throw error;
    }
  }

  async searchNotes(query: string): Promise<SearchResult[]> {
    if (!query.trim()) {
      const notes = await this.getAllNotes();
      return notes.map(note => ({
        id: note.id,
        title: note.title || 'Untitled',
        content: note.content,
        path: [],
        matchType: 'content' as const
      }));
    }

    // Use FTS for full-text search
    const stmt = this.db.prepare(`
      SELECT n.*, 
             snippet(notes_fts, 1, '<mark>', '</mark>', '...', 10) as snippet
      FROM notes n
      JOIN notes_fts fts ON n.id = fts.id
      WHERE notes_fts MATCH ? AND n.is_deleted = FALSE AND n.is_folder = FALSE
      ORDER BY n.is_pinned DESC, n.updated_at DESC
      LIMIT 50
    `);
    
    try {
      const rows = stmt.all(query) as (Note & { snippet: string })[];
      const results: SearchResult[] = [];
      
      for (const row of rows) {
        const path = await this.getNotePath(row.id);
        results.push({
          id: row.id,
          title: row.title || 'Untitled',
          content: row.content,
          snippet: row.snippet,
          path: path.slice(0, -1), // Remove the note itself from path
          matchType: this.getMatchType(row, query)
        });
      }
      
      return results;
    } catch (error) {
      // Fallback to LIKE search if FTS fails
      console.warn('FTS search failed, falling back to LIKE search:', error);
      return this.searchNotesLike(query);
    }
  }

  private async searchNotesLike(query: string): Promise<SearchResult[]> {
    const stmt = this.db.prepare(`
      SELECT * FROM notes 
      WHERE (content LIKE ? OR title LIKE ?) AND is_deleted = FALSE AND is_folder = FALSE
      ORDER BY is_pinned DESC, updated_at DESC
      LIMIT 50
    `);
    
    const searchTerm = `%${query}%`;
    const rows = stmt.all(searchTerm, searchTerm) as Note[];
    const results: SearchResult[] = [];
    
    for (const row of rows) {
      const path = await this.getNotePath(row.id);
      results.push({
        id: row.id,
        title: row.title || 'Untitled',
        content: row.content,
        path: path.slice(0, -1),
        matchType: this.getMatchType(row, query)
      });
    }
    
    return results;
  }

  private getMatchType(note: Note, query: string): 'title' | 'content' {
    const title = note.title || '';
    return title.toLowerCase().includes(query.toLowerCase()) ? 'title' : 'content';
  }

  async getAllNotes(): Promise<Note[]> {
    const stmt = this.db.prepare(`
      SELECT * FROM notes 
      WHERE is_deleted = FALSE
      ORDER BY is_pinned DESC, updated_at DESC
      LIMIT 100
    `);
    
    try {
      const rows = stmt.all();
      return rows as Note[];
    } catch (error) {
      console.error('Error getting all notes:', error);
      throw error;
    }
  }

  async getNoteById(id: string): Promise<Note | null> {
    const stmt = this.db.prepare(`
      SELECT * FROM notes 
      WHERE id = ? AND is_deleted = FALSE
    `);
    
    try {
      const row = stmt.get(id);
      return row as Note | null;
    } catch (error) {
      console.error('Error getting note by id:', error);
      throw error;
    }
  }

  async pinNote(id: string, isPinned: boolean): Promise<void> {
    const stmt = this.db.prepare(`
      UPDATE notes 
      SET is_pinned = ?, updated_at = ?
      WHERE id = ? AND is_deleted = FALSE
    `);
    
    try {
      const result = stmt.run(isPinned, new Date().toISOString(), id);
      if (result.changes === 0) {
        throw new Error('Note not found');
      }
    } catch (error) {
      console.error('Error pinning note:', error);
      throw error;
    }
  }

  // Create folder as a note with is_folder = true
  async createFolder(name: string, parentId?: string, isTemporary?: boolean): Promise<string> {
    const id = uuidv4();
    const now = new Date().toISOString();
    
    const stmt = this.db.prepare(`
      INSERT INTO notes (id, content, title, parent_id, is_folder, is_expanded, is_temporary, created_at, updated_at) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    try {
      stmt.run(id, '', name, parentId || 'root', 1, 1, isTemporary ? 1 : 0, now, now);
      return id;
    } catch (error) {
      console.error('Error creating folder:', error);
      throw error;
    }
  }

  // Move note to different parent
  async moveNote(noteId: string, newParentId: string | null): Promise<void> {
    const stmt = this.db.prepare(`
      UPDATE notes 
      SET parent_id = ?, updated_at = ?
      WHERE id = ? AND is_deleted = FALSE
    `);
    
    try {
      const result = stmt.run(newParentId || 'root', new Date().toISOString(), noteId);
      if (result.changes === 0) {
        throw new Error('Note not found or already deleted');
      }
    } catch (error) {
      console.error('Error moving note:', error);
      throw error;
    }
  }

  // Toggle folder expand/collapse state
  async toggleFolder(folderId: string): Promise<void> {
    const stmt = this.db.prepare(`
      UPDATE notes 
      SET is_expanded = NOT is_expanded, updated_at = ?
      WHERE id = ? AND is_folder = TRUE AND is_deleted = FALSE
    `);
    
    try {
      const result = stmt.run(new Date().toISOString(), folderId);
      if (result.changes === 0) {
        throw new Error('Folder not found or not a folder');
      }
    } catch (error) {
      console.error('Error toggling folder:', error);
      throw error;
    }
  }

  // Get notes organized as tree structure
  async getNotesTree(): Promise<NoteNode[]> {
    const stmt = this.db.prepare(`
      SELECT * FROM notes 
      WHERE is_deleted = FALSE
      ORDER BY is_folder DESC, is_pinned DESC, title ASC, updated_at DESC
    `);
    
    try {
      const rows = stmt.all() as Note[];
      return this.buildTree(rows);
    } catch (error) {
      console.error('Error getting notes tree:', error);
      throw error;
    }
  }

  // Build hierarchical tree from flat notes array
  private buildTree(notes: Note[]): NoteNode[] {
    const nodeMap = new Map<string, NoteNode>();
    const roots: NoteNode[] = [];

    // First pass: create all nodes
    notes.forEach(note => {
      const node: NoteNode = {
        id: note.id,
        title: note.title || (note.is_folder ? 'Untitled Folder' : 'Untitled Note'),
        content: note.content,
        parentId: note.parent_id || null,
        children: [],
        isFolder: note.is_folder || false,
        isExpanded: note.is_expanded || true,
        isPinned: note.is_pinned || false,
        isTemporary: note.is_temporary || false,
        createdAt: new Date(note.created_at),
        updatedAt: new Date(note.updated_at)
      };
      nodeMap.set(note.id, node);
    });

    // Second pass: build parent-child relationships
    nodeMap.forEach(node => {
      if (node.parentId && nodeMap.has(node.parentId)) {
        nodeMap.get(node.parentId)!.children.push(node);
      } else {
        roots.push(node);
      }
    });

    return roots;
  }

  // Get breadcrumb path for a note
  async getNotePath(noteId: string): Promise<string[]> {
    const path: string[] = [];
    let currentId: string | null = noteId;

    while (currentId) {
      const stmt = this.db.prepare(`
        SELECT id, title, parent_id, is_folder FROM notes 
        WHERE id = ? AND is_deleted = FALSE
      `);
      
      const note = stmt.get(currentId) as Note | undefined;
      if (!note) break;

      path.unshift(note.title || (note.is_folder ? 'Untitled Folder' : 'Untitled Note'));
      currentId = note.parent_id || null;
    }

    return path;
  }

  async getAllFolders(): Promise<Folder[]> {
    const stmt = this.db.prepare(`
      SELECT * FROM folders 
      WHERE is_deleted = FALSE
      ORDER BY name ASC
    `);
    
    try {
      const rows = stmt.all();
      return rows as Folder[];
    } catch (error) {
      console.error('Error getting all folders:', error);
      throw error;
    }
  }

  // Cleanup and maintenance
  async cleanup(): Promise<void> {
    // Remove old deleted notes (older than 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const stmt = this.db.prepare(`
      DELETE FROM notes 
      WHERE is_deleted = TRUE AND updated_at < ?
    `);
    
    try {
      const result = stmt.run(thirtyDaysAgo.toISOString());
      console.log(`Cleaned up ${result.changes} old deleted notes`);
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  }

  close(): void {
    this.db.close();
  }
}
