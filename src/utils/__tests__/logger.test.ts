import { SafeLogger } from '../logger';

describe('SafeLogger', () => {
  let originalConsole: Console;

  beforeEach(() => {
    originalConsole = global.console;
  });

  afterEach(() => {
    global.console = originalConsole;
  });

  it('should handle console.log errors gracefully', () => {
    // Mock console.log to throw an error
    global.console = {
      ...originalConsole,
      log: jest.fn(() => {
        throw new Error('EIO: write error');
      }),
    };

    // This should not throw an error
    expect(() => SafeLogger.log('test message')).not.toThrow();
  });

  it('should handle console.warn errors gracefully', () => {
    // Mock console.warn to throw an error
    global.console = {
      ...originalConsole,
      warn: jest.fn(() => {
        throw new Error('EIO: write error');
      }),
    };

    // This should not throw an error
    expect(() => SafeLogger.warn('test warning')).not.toThrow();
  });

  it('should handle console.error errors gracefully', () => {
    // Mock console.error to throw an error
    global.console = {
      ...originalConsole,
      error: jest.fn(() => {
        throw new Error('EIO: write error');
      }),
    };

    // This should not throw an error
    expect(() => SafeLogger.error('test error')).not.toThrow();
  });

  it('should call console methods when they work normally', () => {
    const mockLog = jest.fn();
    const mockWarn = jest.fn();
    const mockError = jest.fn();

    global.console = {
      ...originalConsole,
      log: mockLog,
      warn: mockWarn,
      error: mockError,
    };

    SafeLogger.log('test log');
    SafeLogger.warn('test warn');
    SafeLogger.error('test error');

    expect(mockLog).toHaveBeenCalledWith('test log');
    expect(mockWarn).toHaveBeenCalledWith('test warn');
    expect(mockError).toHaveBeenCalledWith('test error');
  });

  it('should detect console availability correctly', () => {
    // Normal console should be available
    expect(SafeLogger.isConsoleAvailable()).toBe(true);

    // Mock console.log to throw an error
    global.console = {
      ...originalConsole,
      log: jest.fn(() => {
        throw new Error('EIO: write error');
      }),
    };

    // Console should be detected as unavailable
    expect(SafeLogger.isConsoleAvailable()).toBe(false);
  });

  it('should handle flush gracefully', () => {
    // This should not throw even if process.stdout/stderr are unavailable
    expect(() => SafeLogger.flush()).not.toThrow();
  });
});
