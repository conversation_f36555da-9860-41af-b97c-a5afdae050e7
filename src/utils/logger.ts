/**
 * Safe logging utility that handles cases where stdout/stderr streams are unavailable
 * This prevents EIO errors when the process is being terminated or console is disconnected
 */

export class SafeLogger {
  /**
   * Safely log an info message
   */
  static info(...args: any[]): void {
    try {
      console.log(...args);
    } catch (error) {
      // Silently ignore console write errors (e.g., when stdout is unavailable)
    }
  }

  /**
   * Safely log a warning message
   */
  static warn(...args: any[]): void {
    try {
      console.warn(...args);
    } catch (error) {
      // Silently ignore console write errors (e.g., when stdout is unavailable)
    }
  }

  /**
   * Safely log an error message
   */
  static error(...args: any[]): void {
    try {
      console.error(...args);
    } catch (error) {
      // Silently ignore console write errors (e.g., when stdout is unavailable)
    }
  }

  /**
   * Safely log a debug message
   */
  static debug(...args: any[]): void {
    try {
      console.debug(...args);
    } catch (error) {
      // Silently ignore console write errors (e.g., when stdout is unavailable)
    }
  }

  /**
   * Safely log a generic message
   */
  static log(...args: any[]): void {
    try {
      console.log(...args);
    } catch (error) {
      // Silently ignore console write errors (e.g., when stdout is unavailable)
    }
  }

  /**
   * Check if console output is available
   * This can be useful for determining if the process is being terminated
   */
  static isConsoleAvailable(): boolean {
    try {
      // Try a minimal write operation to test if console is available
      console.log('');
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Safely attempt to flush any pending console output
   * Useful before process termination
   */
  static flush(): void {
    try {
      if (process.stdout && typeof process.stdout.write === 'function') {
        process.stdout.write('');
      }
      if (process.stderr && typeof process.stderr.write === 'function') {
        process.stderr.write('');
      }
    } catch (error) {
      // Silently ignore flush errors
    }
  }
}

// Export convenience functions for easier migration
export const safeLog = SafeLogger.log;
export const safeInfo = SafeLogger.info;
export const safeWarn = SafeLogger.warn;
export const safeError = SafeLogger.error;
export const safeDebug = SafeLogger.debug;
