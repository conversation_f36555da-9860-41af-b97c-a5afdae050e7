import { clipboard } from 'electron';
import { v4 as uuidv4 } from 'uuid';
import { ClipboardItem } from '../shared/types';
import { SafeLogger } from '../utils/logger';

export class ClipboardManager {
  private history: ClipboardItem[] = [];
  private lastClipboard = '';
  private watchInterval: NodeJS.Timeout | null = null;
  private readonly maxHistorySize = 100;
  private readonly watchIntervalMs = 1000;

  constructor() {
    this.loadHistory();
  }

  public start() {
    if (this.watchInterval) {
      return; // Already watching
    }

    SafeLogger.info('Starting clipboard monitoring...');
    
    // Get initial clipboard content
    this.lastClipboard = clipboard.readText();
    
    // Start watching for clipboard changes
    this.watchInterval = setInterval(() => {
      this.checkClipboard();
    }, this.watchIntervalMs);
  }

  public stop() {
    if (this.watchInterval) {
      clearInterval(this.watchInterval);
      this.watchInterval = null;
      SafeLogger.info('Stopped clipboard monitoring');
    }
  }

  private checkClipboard() {
    try {
      const currentText = clipboard.readText();
      
      // Check if clipboard content has changed
      if (currentText !== this.lastClipboard && currentText.trim()) {
        this.addToHistory(currentText, 'text');
        this.lastClipboard = currentText;
      }
      
      // TODO: Add support for images and files in future versions
      // const currentImage = clipboard.readImage();
      // if (!currentImage.isEmpty()) {
      //   // Handle image clipboard
      // }
      
    } catch (error) {
      SafeLogger.error('Error checking clipboard:', error);
    }
  }

  private addToHistory(content: string, type: 'text' | 'image' | 'file') {
    // Don't add if it's already the most recent item
    if (this.history.length > 0 && this.history[0].content === content) {
      return;
    }

    // Don't add empty or very short content
    if (content.trim().length < 2) {
      return;
    }

    // Don't add passwords or sensitive data (basic heuristics)
    if (this.isSensitiveContent(content)) {
      return;
    }

    const clipboardItem: ClipboardItem = {
      id: uuidv4(),
      content: content.trim(),
      type,
      created_at: new Date().toISOString(),
    };

    // Add to beginning of history
    this.history.unshift(clipboardItem);

    // Limit history size
    if (this.history.length > this.maxHistorySize) {
      this.history = this.history.slice(0, this.maxHistorySize);
    }

    console.log(`Added to clipboard history: ${content.substring(0, 50)}...`);
    this.saveHistory();
  }

  private isSensitiveContent(content: string): boolean {
    const sensitivePatterns = [
      /password/i,
      /secret/i,
      /token/i,
      /api[_-]?key/i,
      /private[_-]?key/i,
      /ssh[_-]?key/i,
      /credit[_-]?card/i,
      /ssn/i,
      /social[_-]?security/i,
      // Common password patterns
      /^[a-zA-Z0-9!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]{8,}$/,
    ];

    return sensitivePatterns.some(pattern => pattern.test(content));
  }

  public getHistory(): ClipboardItem[] {
    return [...this.history]; // Return a copy
  }

  public clearHistory() {
    this.history = [];
    this.saveHistory();
    console.log('Clipboard history cleared');
  }

  public removeItem(id: string) {
    this.history = this.history.filter(item => item.id !== id);
    this.saveHistory();
  }

  public copyToClipboard(content: string) {
    clipboard.writeText(content);
    this.lastClipboard = content;
  }

  private saveHistory() {
    try {
      // In a real app, you might want to save to a file or database
      // For now, we'll keep it in memory only
      // TODO: Implement persistent storage
    } catch (error) {
      SafeLogger.error('Error saving clipboard history:', error);
    }
  }

  private loadHistory() {
    try {
      // TODO: Load from persistent storage
      // For now, start with empty history
      this.history = [];
    } catch (error) {
      SafeLogger.error('Error loading clipboard history:', error);
      this.history = [];
    }
  }

  public searchHistory(query: string): ClipboardItem[] {
    if (!query.trim()) {
      return this.getHistory();
    }

    const searchTerm = query.toLowerCase();
    return this.history.filter(item =>
      item.content.toLowerCase().includes(searchTerm)
    );
  }
}
